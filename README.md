# 🚀 ZIP → GitHub Repo Uploader

Ein intelligentes Werkzeug, um ZIP-Projekte direkt in GitHub-Repositories umzuwandeln – **automatisiert, elegant, erweiterbar**.

Dieses Tool ist Teil deines `app-dev-starter`-Projekts und dient als **perfekte Vorlage**, um strukturierte Workflows und Upload-Prozesse zu lernen und zu wiederholen.

---

## 🎯 Funktionen

- 📦 Lade ZIP-Dateien direkt über die Oberfläche hoch
- 🧠 Die App entpackt, initialisiert Git, committet und pusht dein Projekt
- 🌐 Erstellt automatisch ein neues **öffentliches Repository auf GitHub**
- ⚙️ Nutzt GitHub API (via Token) und subprocess für Git-Aktionen
- 🧱 Ideal zur Massenverarbeitung von ZIP-Projekten (z. B. von Exporten, Prototypen, Bot-Projekten)

---

## 🧪 Demo in Aktion

> Ein ZIP hochladen. Projektname & Token eingeben. **Zack, online.**

---

## 🛠️ Voraussetzungen

- Python 3.8+
- Git (im Systempfad)
- [GitHub Token](https://github.com/settings/tokens) mit `repo`-Rechten

Installieren:
```bash
pip install -r requirements.txt
```

Starten:
```bash
streamlit run streamlit_app.py
```

---

## 📁 Projektstruktur

```text
zip-uploader/
├── streamlit_app.py            ← Hauptanwendung (Streamlit GUI)
├── uploader_utils.py           ← GitHub- & Git-Integration
├── requirements.txt            ← Python-Abhängigkeiten
├── README.md                   ← Diese Datei
```

---

## 🧑‍💻 Vorlage von

**swisscomfort** – entwickelt im Rahmen des `app-dev-starter`-Setups  
👀 [Besuche mein GitHub-Profil](https://github.com/swisscomfort)