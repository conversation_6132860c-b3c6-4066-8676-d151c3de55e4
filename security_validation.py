import os
import zipfile
import magic
import hashlib
import re

# Maximale Dateigröße (50 MB)
MAX_FILE_SIZE = 50 * 1024 * 1024

# Blacklist für gefährliche Dateitypen
BLACKLISTED_EXTENSIONS = [
    '.exe', '.dll', '.bat', '.cmd', '.sh', '.com', '.scr', '.pif', '.vbs', '.ps1'
]

# Blacklist für verdächtige Dateinamen
SUSPICIOUS_PATTERNS = [
    r'backdoor', r'hack', r'exploit', r'trojan', r'virus', r'malware', r'ransom'
]

def is_safe_filename(filename):
    """Prüft, ob ein Dateiname verdächtige Muster enthält"""
    filename_lower = filename.lower()
    
    # Prüfe auf Blacklist-Erweiterungen
    if any(filename_lower.endswith(ext) for ext in BLACKLISTED_EXTENSIONS):
        return False
    
    # Prüfe auf verdächtige Muster
    if any(re.search(pattern, filename_lower) for pattern in SUSPICIOUS_PATTERNS):
        return False
    
    return True

def validate_zip_file(zip_path):
    """
    Validiert eine ZIP-Datei auf Sicherheitsrisiken.
    Gibt (is_valid, message) zurück.
    """
    # Prüfe Dateigröße
    if os.path.getsize(zip_path) > MAX_FILE_SIZE:
        return False, f"ZIP-Datei ist zu groß (max. {MAX_FILE_SIZE/1024/1024} MB)"
    
    try:
        # Prüfe, ob es sich um eine gültige ZIP-Datei handelt
        if not zipfile.is_zipfile(zip_path):
            return False, "Keine gültige ZIP-Datei"
        
        # Öffne ZIP und prüfe Inhalt
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            # Prüfe auf zu viele Dateien
            if len(zip_ref.namelist()) > 1000:
                return False, "ZIP enthält zu viele Dateien (max. 1000)"
            
            # Prüfe auf verdächtige Dateinamen
            for filename in zip_ref.namelist():
                if not is_safe_filename(filename):
                    return False, f"Verdächtiger Dateiname gefunden: {filename}"
            
            # Prüfe auf verschachtelte ZIPs (ZIP-Bomb-Schutz)
            total_size = 0
            for info in zip_ref.infolist():
                if info.file_size > MAX_FILE_SIZE:
                    return False, f"Einzelne Datei zu groß: {info.filename}"
                
                total_size += info.file_size
                if total_size > MAX_FILE_SIZE * 2:  # Entpackte Größe darf max. doppelt so groß sein
                    return False, "Entpackte Größe überschreitet Limit (ZIP-Bomb-Verdacht)"
        
        return True, "ZIP-Datei ist sicher"
    
    except Exception as e:
        return False, f"Fehler bei der Validierung: {str(e)}"

def scan_file_content(file_path):
    """
    Scannt den Inhalt einer Datei auf verdächtige Muster.
    In einer vollständigen Implementierung würde hier ein Virenscanner integriert.
    """
    # Einfache Prüfung des MIME-Types
    mime = magic.Magic(mime=True)
    file_type = mime.from_file(file_path)
    
    # Verdächtige MIME-Typen
    suspicious_mimes = [
        'application/x-dosexec',
        'application/x-executable',
        'application/x-sharedlib',
        'application/x-msdos-program'
    ]
    
    if file_type in suspicious_mimes:
        return False, f"Verdächtiger Dateityp erkannt: {file_type}"
    
    # Prüfe Datei-Hash gegen bekannte Malware-Hashes
    # In einer vollständigen Implementierung würde hier eine Datenbank mit bekannten
    # Malware-Hashes abgefragt werden
    file_hash = calculate_file_hash(file_path)
    
    # Beispiel für eine einfache Prüfung (in der Praxis würde man eine echte Datenbank verwenden)
    known_malware_hashes = []  # Hier würden bekannte Malware-Hashes stehen
    
    if file_hash in known_malware_hashes:
        return False, "Datei entspricht bekannter Malware"
    
    # Prüfe auf verdächtige Byte-Sequenzen
    # Dies ist nur ein einfaches Beispiel - in der Praxis würde man
    # fortschrittlichere Heuristiken oder einen echten Virenscanner verwenden
    with open(file_path, 'rb') as f:
        content = f.read(4096)  # Lese die ersten 4KB
        
        # Beispiel: Suche nach MZ-Header (typisch für Windows-Executables)
        if content.startswith(b'MZ') and file_path.endswith('.txt'):
            return False, "Verdächtige Byte-Sequenz gefunden (versteckte ausführbare Datei)"
    
    return True, "Datei scheint sicher zu sein"

def calculate_file_hash(file_path):
    """Berechnet den SHA-256 Hash einer Datei"""
    sha256 = hashlib.sha256()
    
    with open(file_path, 'rb') as f:
        # Lese die Datei in Blöcken, um Speicherverbrauch zu minimieren
        for block in iter(lambda: f.read(4096), b''):
            sha256.update(block)
    
    return sha256.hexdigest()

def validate_file_upload(file_path):
    """
    Führt eine vollständige Sicherheitsvalidierung für eine hochgeladene Datei durch.
    Kombiniert Dateinamen-, Größen- und Inhaltsüberprüfung.
    """
    # Prüfe Dateiname
    filename = os.path.basename(file_path)
    if not is_safe_filename(filename):
        return False, f"Verdächtiger Dateiname: {filename}"
    
    # Prüfe Dateigröße
    if os.path.getsize(file_path) > MAX_FILE_SIZE:
        return False, f"Datei ist zu groß (max. {MAX_FILE_SIZE/1024/1024} MB)"
    
    # Prüfe Dateiinhalt
    is_safe, message = scan_file_content(file_path)
    if not is_safe:
        return False, message
    
    return True, "Datei ist sicher"

def validate_upload_directory(directory_path):
    """
    Validiert alle Dateien in einem Verzeichnis.
    Nützlich nach dem Entpacken eines ZIP-Archivs.
    """
    results = []
    
    for root, _, files in os.walk(directory_path):
        for filename in files:
            file_path = os.path.join(root, filename)
            is_safe, message = validate_file_upload(file_path)
            results.append({
                "file": file_path,
                "is_safe": is_safe,
                "message": message
            })
    
    # Prüfe, ob alle Dateien sicher sind
    all_safe = all(result["is_safe"] for result in results)
    
    if all_safe:
        return True, "Alle Dateien sind sicher"
    else:
        # Finde die erste unsichere Datei
        unsafe_file = next(result for result in results if not result["is_safe"])
        return False, f"Unsichere Datei gefunden: {unsafe_file['file']} - {unsafe_file['message']}"
