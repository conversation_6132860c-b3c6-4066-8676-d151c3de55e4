import os
import subprocess
import requests
import logging
import json
from datetime import datetime

# Logging konfigurieren
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_repo_and_push(github_token: str, github_user: str, repo_name: str, 
                         local_path: str, private=True, license_template=None, 
                         gitignore_template=None, auto_init=True) -> str:
    """
    Erstellt ein neues GitHub-Repository per REST-API und pusht ein lokales Projektverzeichnis.
    """
    # GitHub-API-Aufruf
    headers = {
        "Authorization": f"token {github_token}",
        "Accept": "application/vnd.github.v3+json"
    }

    # Stelle sicher, dass der Repository-Name gültig ist
    repo_name = repo_name.strip().replace(" ", "-")
    
    data = {
        "name": repo_name,
        "private": private,
        "description": "Automatisch erstellt mit ZIP-Uploader"
    }
    
    # Optionale Parameter hinzufügen
    if license_template and license_template != "Keine":
        data["license_template"] = license_template
    if gitignore_template and gitignore_template != "Keine":
        data["gitignore_template"] = gitignore_template
    if auto_init:
        data["auto_init"] = True

    logger.info(f"Erstelle Repository: {repo_name}")
    response = requests.post("https://api.github.com/user/repos", headers=headers, json=data)
    
    if response.status_code != 201:
        error_details = response.json()
        error_message = error_details.get('message', 'Unbekannter Fehler')
        errors = error_details.get('errors', [])
        
        if errors:
            error_info = f"{error_message}. Details: {', '.join([err.get('message', str(err)) for err in errors])}"
        else:
            error_info = error_message
            
        logger.error(f"GitHub API-Fehler ({response.status_code}): {error_info}")
        raise RuntimeError(f"GitHub API-Fehler ({response.status_code}): {error_info}")

    repo_data = response.json()
    clone_url = repo_data["clone_url"]
    auth_url = clone_url.replace("https://", f"https://{github_token}@")
    
    logger.info(f"Repository erfolgreich erstellt: {clone_url}")

    # Git-Initialisierung
    if not os.path.isdir(local_path):
        raise RuntimeError(f"Lokaler Pfad existiert nicht: {local_path}")

    # Erstelle .gitignore, falls nicht vorhanden
    gitignore_path = os.path.join(local_path, ".gitignore")
    if not os.path.exists(gitignore_path):
        with open(gitignore_path, "w") as f:
            f.write("# Automatisch generierte .gitignore\n")
            f.write("node_modules/\n.DS_Store\n*.log\n")

    # Git-Befehle mit Fehlerbehandlung
    try:
        subprocess.run(["git", "init"], cwd=local_path, check=True)
        subprocess.run(["git", "checkout", "-b", "main"], cwd=local_path, check=True)
        
        # Füge alle Dateien hinzu, ignoriere Fehler bei einzelnen Dateien
        try:
            subprocess.run(["git", "add", "-f", "."], cwd=local_path, check=True)
        except subprocess.CalledProcessError:
            # Versuche es mit einer alternativen Methode, wenn der erste Versuch fehlschlägt
            logger.warning("Standardmethode zum Hinzufügen von Dateien fehlgeschlagen, versuche alternative Methode...")
            # Füge Dateien einzeln hinzu
            for root, _, files in os.walk(local_path):
                for file in files:
                    if not file.startswith(".git"):
                        file_path = os.path.join(root, file)
                        rel_path = os.path.relpath(file_path, local_path)
                        try:
                            subprocess.run(["git", "add", "-f", rel_path], cwd=local_path)
                        except Exception as e:
                            logger.warning(f"Konnte Datei nicht hinzufügen: {rel_path}, Fehler: {str(e)}")
        
        subprocess.run(["git", "commit", "-m", "🚀 Automatischer Upload via Streamlit"], cwd=local_path, check=True)
        subprocess.run(["git", "remote", "add", "origin", auth_url], cwd=local_path, check=True)
        subprocess.run(["git", "push", "-u", "origin", "main"], cwd=local_path, check=True)
        logger.info(f"Git-Push erfolgreich abgeschlossen")
    except subprocess.CalledProcessError as e:
        logger.error(f"Git-Fehler: {str(e)}")
        raise RuntimeError(f"Git-Fehler: {str(e)}")

    # Upload-Historie speichern
    save_upload_history(repo_name, clone_url)
    
    return clone_url

def save_upload_history(repo_name, repo_url):
    """Speichert Upload-Historie in einer JSON-Datei"""
    history_file = "upload_history.json"
    history = []
    
    if os.path.exists(history_file):
        try:
            with open(history_file, 'r') as f:
                history = json.load(f)
        except json.JSONDecodeError:
            logger.warning(f"Fehler beim Lesen der Upload-Historie, erstelle neue Datei")
    
    history.append({
        "repo_name": repo_name,
        "repo_url": repo_url,
        "timestamp": datetime.now().isoformat(),
        "status": "success"
    })
    
    with open(history_file, 'w') as f:
        json.dump(history, f, indent=2)
    
    logger.info(f"Upload-Historie aktualisiert: {repo_name}")
